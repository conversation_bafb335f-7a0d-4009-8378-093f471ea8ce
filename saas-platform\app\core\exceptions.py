"""
自定义异常类
定义应用程序中使用的各种异常类型
"""

from typing import Any, Dict, Optional


class AstrBotException(Exception):
    """AstrBot基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(AstrBotException):
    """数据验证异常"""
    pass


class AuthenticationError(AstrBotException):
    """认证异常"""
    pass


class AuthorizationError(AstrBotException):
    """授权异常"""
    pass


class NotFoundError(AstrBotException):
    """资源未找到异常"""
    pass


class ConflictError(AstrBotException):
    """资源冲突异常"""
    pass


class DatabaseError(AstrBotException):
    """数据库操作异常"""
    pass


class ExternalServiceError(AstrBotException):
    """外部服务异常"""
    pass


class ConfigurationError(AstrBotException):
    """配置错误异常"""
    pass


class RateLimitError(AstrBotException):
    """速率限制异常"""
    pass


class LLMProviderError(AstrBotException):
    """LLM提供商异常"""
    pass


class TenantError(AstrBotException):
    """租户相关异常"""
    pass


class UserError(AstrBotException):
    """用户相关异常"""
    pass


class SessionError(AstrBotException):
    """会话相关异常"""
    pass


class MessageError(AstrBotException):
    """消息相关异常"""
    pass


class IntegrationError(AstrBotException):
    """集成相关异常"""
    pass


class PermissionError(AstrBotException):
    """权限相关异常"""
    pass


class QuotaExceededError(AstrBotException):
    """配额超限异常"""
    pass


class ServiceUnavailableError(AstrBotException):
    """服务不可用异常"""
    pass


class TimeoutError(AstrBotException):
    """超时异常"""
    pass


class InvalidTokenError(AuthenticationError):
    """无效令牌异常"""
    pass


class ExpiredTokenError(AuthenticationError):
    """令牌过期异常"""
    pass


class InsufficientPermissionsError(AuthorizationError):
    """权限不足异常"""
    pass


class ResourceNotFoundError(NotFoundError):
    """资源未找到异常"""
    pass


class DuplicateResourceError(ConflictError):
    """重复资源异常"""
    pass


class DatabaseConnectionError(DatabaseError):
    """数据库连接异常"""
    pass


class DatabaseTransactionError(DatabaseError):
    """数据库事务异常"""
    pass


class LLMConnectionError(LLMProviderError):
    """LLM连接异常"""
    pass


class LLMQuotaExceededError(LLMProviderError):
    """LLM配额超限异常"""
    pass


class LLMResponseError(LLMProviderError):
    """LLM响应异常"""
    pass


class TenantNotFoundError(TenantError):
    """租户未找到异常"""
    pass


class TenantInactiveError(TenantError):
    """租户未激活异常"""
    pass


class UserNotFoundError(UserError):
    """用户未找到异常"""
    pass


class UserInactiveError(UserError):
    """用户未激活异常"""
    pass


class SessionNotFoundError(SessionError):
    """会话未找到异常"""
    pass


class SessionExpiredError(SessionError):
    """会话过期异常"""
    pass


class MessageNotFoundError(MessageError):
    """消息未找到异常"""
    pass


class MessageTooLongError(MessageError):
    """消息过长异常"""
    pass


class IntegrationNotFoundError(IntegrationError):
    """集成未找到异常"""
    pass


class IntegrationConfigError(IntegrationError):
    """集成配置异常"""
    pass


class APIKeyError(AuthenticationError):
    """API密钥异常"""
    pass


class WebhookError(ExternalServiceError):
    """Webhook异常"""
    pass


class FileUploadError(AstrBotException):
    """文件上传异常"""
    pass


class FileProcessingError(AstrBotException):
    """文件处理异常"""
    pass


class CacheError(AstrBotException):
    """缓存异常"""
    pass


class QueueError(AstrBotException):
    """队列异常"""
    pass


class SchedulerError(AstrBotException):
    """调度器异常"""
    pass


class MonitoringError(AstrBotException):
    """监控异常"""
    pass


class BackupError(AstrBotException):
    """备份异常"""
    pass


class RestoreError(AstrBotException):
    """恢复异常"""
    pass


class MigrationError(AstrBotException):
    """迁移异常"""
    pass


class HealthCheckError(AstrBotException):
    """健康检查异常"""
    pass


class MetricsError(AstrBotException):
    """指标异常"""
    pass


class LoggingError(AstrBotException):
    """日志异常"""
    pass


class SecurityError(AstrBotException):
    """安全异常"""
    pass


class EncryptionError(SecurityError):
    """加密异常"""
    pass


class DecryptionError(SecurityError):
    """解密异常"""
    pass


class HashingError(SecurityError):
    """哈希异常"""
    pass


class SignatureError(SecurityError):
    """签名异常"""
    pass


class CSRFError(SecurityError):
    """CSRF异常"""
    pass


class XSSError(SecurityError):
    """XSS异常"""
    pass


class SQLInjectionError(SecurityError):
    """SQL注入异常"""
    pass


class BruteForceError(SecurityError):
    """暴力破解异常"""
    pass


class SuspiciousActivityError(SecurityError):
    """可疑活动异常"""
    pass
