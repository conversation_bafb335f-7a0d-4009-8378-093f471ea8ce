#!/usr/bin/env python3
"""
简单配置测试
"""

import os
from pathlib import Path
from pydantic import BaseModel
from pydantic_settings import BaseSettings

# 切换到saas-platform目录
saas_dir = Path(__file__).parent / "saas-platform"
os.chdir(saas_dir)

print(f"当前工作目录: {os.getcwd()}")
print(f".env文件存在: {Path('.env').exists()}")

# 读取.env文件内容
if Path('.env').exists():
    with open('.env', 'r', encoding='utf-8') as f:
        content = f.read()
    print(f".env文件内容:\n{content[:500]}...")

# 测试简单的Settings类
class SimpleSettings(BaseSettings):
    FIRST_SUPERUSER: str = "<EMAIL>"
    FIRST_SUPERUSER_PASSWORD: str = "default123"
    SECRET_KEY: str = "default-secret"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # 忽略额外字段

try:
    print("\n测试简单配置...")
    simple_settings = SimpleSettings()
    print(f"✅ 简单配置成功!")
    print(f"FIRST_SUPERUSER: {simple_settings.FIRST_SUPERUSER}")
    print(f"FIRST_SUPERUSER_PASSWORD: {simple_settings.FIRST_SUPERUSER_PASSWORD}")
    print(f"SECRET_KEY: {simple_settings.SECRET_KEY[:20]}...")
    
except Exception as e:
    print(f"❌ 简单配置失败: {e}")
    import traceback
    traceback.print_exc()

# 测试环境变量
print(f"\n环境变量测试:")
print(f"FIRST_SUPERUSER_PASSWORD: {os.environ.get('FIRST_SUPERUSER_PASSWORD', 'NOT_SET')}")
print(f"SECRET_KEY: {os.environ.get('SECRET_KEY', 'NOT_SET')[:20] if os.environ.get('SECRET_KEY') else 'NOT_SET'}...")
