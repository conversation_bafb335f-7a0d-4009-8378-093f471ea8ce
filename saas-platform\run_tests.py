#!/usr/bin/env python3
"""
AstrBot SaaS 快速测试启动脚本
提供简单的测试执行入口
"""

import sys
import subprocess
from pathlib import Path


def run_command(cmd: str, description: str):
    """执行命令并显示结果"""
    print(f"\n🚀 {description}")
    print("=" * 60)

    # NOTE: shell=True used for Windows compatibility - ensure input is sanitized
    result = subprocess.run(cmd, shell=True, cwd=Path(__file__).parent)

    if result.returncode == 0:
        print(f"✅ {description} - 成功")
    else:
        print(f"❌ {description} - 失败 (退出码: {result.returncode})")

    return result.returncode


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print(
            """
🧪 AstrBot SaaS 测试工具

使用方法:
  python run_tests.py <command>

可用命令:
  unit        - 运行单元测试
  integration - 运行集成测试  
  e2e         - 运行端到端测试
  all         - 运行所有测试
  coverage    - 运行测试并生成覆盖率报告
  quality     - 运行完整质量检查
  smart       - 智能测试选择
  
示例:
  python run_tests.py unit
  python run_tests.py quality
  python run_tests.py smart --dry-run
        """
        )
        sys.exit(1)

    command = sys.argv[1].lower()

    if command == "unit":
        exit_code = run_command("python -m pytest tests/unit/ -v", "单元测试")

    elif command == "integration":
        exit_code = run_command("python -m pytest tests/integration/ -v", "集成测试")

    elif command == "e2e":
        exit_code = run_command("python -m pytest tests/e2e/ -v", "端到端测试")

    elif command == "all":
        exit_code = run_command("python -m pytest tests/ -v", "所有测试")

    elif command == "coverage":
        exit_code = run_command(
            "python -m pytest --cov=app --cov-report=html --cov-report=term tests/",
            "测试覆盖率",
        )
        if exit_code == 0:
            print("\n📊 覆盖率报告已生成: htmlcov/index.html")

    elif command == "quality":
        exit_code = run_command("python scripts/run_quality_checks.py", "质量检查")

    elif command == "smart":
        # 传递额外参数给智能测试选择器
        extra_args = " ".join(sys.argv[2:])
        exit_code = run_command(
            f"python tests/helpers/smart_test_selector.py {extra_args}", "智能测试选择"
        )

    else:
        print(f"❌ 未知命令: {command}")
        print("运行 'python run_tests.py' 查看可用命令")
        sys.exit(1)

    sys.exit(exit_code)


if __name__ == "__main__":
    main()
