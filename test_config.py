#!/usr/bin/env python3
"""
测试配置加载
"""

import os
import sys
from pathlib import Path

# 添加saas-platform到路径
saas_dir = Path(__file__).parent / "saas-platform"
sys.path.insert(0, str(saas_dir))

# 设置工作目录
os.chdir(saas_dir)

# 设置环境变量
os.environ['BACKEND_CORS_ORIGINS'] = 'http://localhost:3000,http://localhost:8080,http://localhost:6185'
os.environ['FIRST_SUPERUSER'] = '<EMAIL>'
os.environ['FIRST_SUPERUSER_PASSWORD'] = 'ChangeMeASAP!'
os.environ['DATABASE_URL'] = 'postgresql+asyncpg://astrbot:astrbot123@localhost:5432/astrbot_saas'
os.environ['REDIS_URL'] = 'redis://:redis123@localhost:6379/0'
os.environ['SECRET_KEY'] = '09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7'

try:
    from app.core.config.settings import Settings
    
    print("测试配置加载...")
    settings = Settings()
    print(f"✅ 配置加载成功!")
    print(f"CORS Origins: {settings.BACKEND_CORS_ORIGINS}")
    print(f"Database URL: {settings.DATABASE_URL}")
    print(f"Redis URL: {settings.REDIS_URL}")
    
except Exception as e:
    print(f"❌ 配置加载失败: {e}")
    import traceback
    traceback.print_exc()
