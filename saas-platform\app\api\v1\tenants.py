"""
租户管理API端点
提供租户的CRUD操作接口，支持多租户隔离和权限控制
"""

from typing import Any, Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, Header, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_admin_user
from app.core.database import get_db
from app.models.tenant import Tenant
from app.models.user import User
from app.schemas.common import PaginatedResponse, StandardResponse
from app.schemas.tenant import TenantCreate, TenantRead, TenantUpdate
from app.services.tenant_service import TenantService, get_tenant_service
from app.utils.logging import get_logger

# 创建路由器
router = APIRouter(prefix="/tenants", tags=["租户管理"])
logger = get_logger(__name__)


def get_tenant_from_mixed_auth():
    """
    获取租户的混合认证依赖
    支持API key和JWT token两种认证方式
    优先使用API key认证（用于测试和webhook）
    """

    async def _get_tenant(
        x_api_key: Optional[str] = Header(None, alias="X-API-Key"),
        authorization: Optional[str] = Header(None, alias="Authorization"),
        db: AsyncSession = Depends(get_db),
    ) -> Tenant:
        # 优先使用API key认证
        if x_api_key:
            try:
                from sqlalchemy import select

                stmt = select(Tenant).where(Tenant.api_key == x_api_key)
                result = await db.execute(stmt)
                tenant = result.scalar_one_or_none()

                if tenant and tenant.is_active:
                    logger.info(
                        "API key认证成功",
                        tenant_id=str(tenant.id),
                        api_key=x_api_key[:8] + "***",
                    )
                    return tenant
                else:
                    logger.warning(
                        "API key认证失败：租户不存在或未激活",
                        api_key=x_api_key[:8] + "***",
                        tenant_found=tenant is not None,
                        tenant_active=tenant.is_active if tenant else None,
                    )
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="API key authentication failed",
                    )
            except HTTPException:
                raise
            except Exception as e:
                logger.error("API key认证异常", error=str(e), exc_info=True)
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="API key authentication failed",
                )

        # JWT认证fallback（从现有的get_current_tenant获取逻辑）
        if authorization and authorization.startswith("Bearer "):
            try:
                # 简化实现：使用现有的JWT认证逻辑
                # 注意：这里需要从get_current_tenant中提取JWT验证逻辑
                from sqlalchemy import select

                from app.core.security import get_subject_from_token

                token = authorization.replace("Bearer ", "")
                user_id = get_subject_from_token(token)

                # 查找用户对应的租户
                stmt = select(Tenant).join(User).where(User.id == user_id)
                result = await db.execute(stmt)
                tenant = result.scalar_one_or_none()

                if tenant and tenant.is_active:
                    logger.info(
                        "JWT认证成功", tenant_id=str(tenant.id), user_id=user_id
                    )
                    return tenant
                else:
                    logger.warning(
                        "JWT认证失败：租户不存在或未激活",
                        user_id=user_id,
                        tenant_found=tenant is not None,
                    )
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="JWT authentication failed",
                    )
            except HTTPException:
                raise
            except Exception as e:
                logger.error("JWT认证异常", error=str(e), exc_info=True)
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="JWT authentication failed",
                )

        # 如果所有认证方式都失败
        logger.warning("认证失败: 缺少有效的认证凭据")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required. Please provide API key or valid JWT token.",
        )

    return _get_tenant


@router.post(
    "",
    response_model=StandardResponse[TenantRead],
    status_code=status.HTTP_201_CREATED,
    summary="创建租户",
    description="创建新的租户，支持企业注册流程",
)
async def create_tenant(
    tenant_data: TenantCreate,
    tenant_service: TenantService = Depends(get_tenant_service),
) -> StandardResponse[TenantRead]:
    """
    创建新租户

    - **name**: 企业名称（必须唯一）
    - **email**: 联系邮箱（必须唯一）
    - **plan**: 订阅计划
    - **metadata**: 扩展元数据
    """
    try:
        logger.info(
            "开始创建租户", tenant_name=tenant_data.name, email=tenant_data.email
        )

        # 创建租户
        new_tenant = await tenant_service.create_tenant(tenant_data)

        logger.info(
            "租户创建成功", tenant_id=str(new_tenant.id), tenant_name=new_tenant.name
        )

        return StandardResponse(success=True, message="租户创建成功", data=new_tenant)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("租户创建异常", error=str(e), tenant_data=tenant_data.model_dump())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="租户创建失败，请稍后重试",
        )


@router.get(
    "/{tenant_id}",
    response_model=StandardResponse[TenantRead],
    summary="获取租户详情",
    description="根据租户ID获取租户详细信息，支持API密钥和JWT认证",
)
async def get_tenant(
    tenant_id: UUID,
    current_tenant: Tenant = Depends(get_tenant_from_mixed_auth()),
    tenant_service: TenantService = Depends(get_tenant_service),
) -> StandardResponse[TenantRead]:
    """
    获取租户详情

    - **tenant_id**: 租户唯一标识

    注意：用户只能访问自己所属租户的信息
    """
    try:
        # 租户隔离检查：用户只能访问自己的租户信息
        if current_tenant.id != tenant_id:
            logger.warning(
                "租户访问权限被拒绝",
                current_tenant_id=str(current_tenant.id),
                requested_tenant_id=str(tenant_id),
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有权限访问该租户信息"
            )

        # 获取租户信息
        tenant = await tenant_service.get_tenant(tenant_id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="租户不存在"
            )

        logger.info(
            "租户信息获取成功",
            tenant_id=str(tenant_id),
            current_user_tenant=str(current_tenant.id),
        )

        return StandardResponse(success=True, message="获取租户信息成功", data=tenant)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("租户信息获取异常", error=str(e), tenant_id=str(tenant_id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取租户信息失败"
        )


@router.put(
    "/{tenant_id}",
    response_model=StandardResponse[TenantRead],
    summary="更新租户信息",
    description="更新租户基本信息，支持部分字段更新",
)
async def update_tenant(
    tenant_id: UUID,
    tenant_data: TenantUpdate,
    current_tenant: Tenant = Depends(
        get_tenant_from_mixed_auth()
    ),  # ✅ 修复：统一使用混合认证
    tenant_service: TenantService = Depends(get_tenant_service),
) -> StandardResponse[TenantRead]:
    """
    更新租户信息

    - **tenant_id**: 租户唯一标识
    - **tenant_data**: 更新的租户数据

    注意：用户只能更新自己所属的租户信息
    """
    try:
        # 租户隔离检查：用户只能更新自己的租户信息
        if current_tenant.id != tenant_id:
            logger.warning(
                "租户更新权限被拒绝",
                current_tenant_id=str(current_tenant.id),
                requested_tenant_id=str(tenant_id),
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有权限更新该租户信息"
            )

        # 更新租户信息
        updated_tenant = await tenant_service.update_tenant(tenant_id, tenant_data)
        if not updated_tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="租户不存在"
            )

        logger.info(
            "租户信息更新成功",
            tenant_id=str(tenant_id),
            updated_fields=list(tenant_data.model_dump(exclude_unset=True).keys()),
        )

        return StandardResponse(
            success=True, message="租户信息更新成功", data=updated_tenant
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "租户信息更新异常",
            error=str(e),
            tenant_id=str(tenant_id),
            tenant_data=tenant_data.model_dump(),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新租户信息失败"
        )


@router.delete(
    "/{tenant_id}",
    response_model=StandardResponse[bool],
    summary="删除租户",
    description="软删除租户，支持数据恢复",
)
async def delete_tenant(
    tenant_id: UUID,
    current_tenant: Tenant = Depends(
        get_tenant_from_mixed_auth()
    ),  # ✅ 修复：统一使用混合认证
    tenant_service: TenantService = Depends(get_tenant_service),
) -> StandardResponse[bool]:
    """
    删除租户（软删除）

    - **tenant_id**: 租户唯一标识

    注意：用户只能删除自己所属的租户，删除为软删除可恢复
    """
    try:
        # 租户隔离检查：用户只能删除自己的租户
        if current_tenant.id != tenant_id:
            logger.warning(
                "租户删除权限被拒绝",
                current_tenant_id=str(current_tenant.id),
                requested_tenant_id=str(tenant_id),
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有权限删除该租户"
            )

        # 检查租户是否存在
        existing_tenant = await tenant_service.get_tenant(tenant_id)
        if not existing_tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="租户不存在"
            )

        # 执行软删除
        success = await tenant_service.delete_tenant(tenant_id)

        logger.info(
            "租户删除成功", tenant_id=str(tenant_id), tenant_name=existing_tenant.name
        )

        return StandardResponse(success=True, message="租户删除成功", data=success)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("租户删除异常", error=str(e), tenant_id=str(tenant_id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除租户失败"
        )


@router.get(
    "",
    response_model=PaginatedResponse[TenantRead],
    summary="获取租户列表",
    description="分页获取租户列表，支持搜索和过滤（管理员专用或使用API Key的租户只能看到自己）",
)
async def list_tenants(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    current_tenant: Tenant = Depends(
        get_tenant_from_mixed_auth()
    ),  # ✅ 修复：改为混合认证，普通租户只能看到自己
    tenant_service: TenantService = Depends(get_tenant_service),
) -> PaginatedResponse[TenantRead]:
    """
    获取租户列表

    - **skip**: 跳过的记录数（分页）
    - **limit**: 每页记录数（分页）
    - **search**: 搜索关键词
    - **is_active**: 过滤激活状态

    注意：普通租户用户只能查看自己的租户信息，不支持列表查询
    """
    try:
        # 非管理员用户只能看到自己的租户信息（单个结果的"列表"）
        logger.info(
            "租户列表查询",
            tenant_id=str(current_tenant.id),
            skip=skip,
            limit=limit,
            search=search,
            is_active=is_active,
        )

        # 对于API Key认证的普通租户，只返回自己的信息
        tenants = [current_tenant]
        total = 1

        # 构造响应
        logger.info(
            "租户列表查询成功",
            current_tenant_id=str(current_tenant.id),
            returned_count=len(tenants),
            total=total,
        )

        return PaginatedResponse(
            success=True,
            message="获取租户列表成功",
            data=tenants,
            total=total,
            page=skip // limit + 1 if limit > 0 else 1,
            page_size=limit,
            pages=(total + limit - 1) // limit if limit > 0 else 1,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "租户列表查询异常", error=str(e), current_tenant_id=str(current_tenant.id)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取租户列表失败"
        )


@router.get(
    "/{tenant_id}/stats",
    response_model=StandardResponse[dict],
    summary="获取租户统计信息",
    description="获取租户的统计数据，包括用户数、会话数等",
)
async def get_tenant_statistics(
    tenant_id: UUID,
    current_tenant: Tenant = Depends(
        get_tenant_from_mixed_auth()
    ),  # ✅ 修复：统一使用混合认证
    tenant_service: TenantService = Depends(get_tenant_service),
) -> StandardResponse[dict]:
    """
    获取租户统计信息

    - **tenant_id**: 租户唯一标识

    返回租户的统计数据，如用户数、会话数、消息数等
    """
    try:
        # 租户隔离检查：用户只能查看自己租户的统计
        if current_tenant.id != tenant_id:
            logger.warning(
                "租户统计访问权限被拒绝",
                current_tenant_id=str(current_tenant.id),
                requested_tenant_id=str(tenant_id),
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该租户统计信息",
            )

        # 获取统计信息
        stats = await tenant_service.get_tenant_statistics(tenant_id)

        logger.info(
            "租户统计信息获取成功",
            tenant_id=str(tenant_id),
            stats_keys=list(stats.keys()),
        )

        return StandardResponse(
            success=True, message="获取租户统计信息成功", data=stats
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("租户统计信息获取异常", error=str(e), tenant_id=str(tenant_id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取租户统计信息失败",
        )


@router.patch(
    "/{tenant_id}/status",
    response_model=StandardResponse[TenantRead],
    summary="更新租户状态",
    description="更新租户激活状态（启用/禁用）",
)
async def update_tenant_status(
    tenant_id: UUID,
    status_update: dict = Body(..., example={"is_active": True}),
    current_tenant: Tenant = Depends(
        get_tenant_from_mixed_auth()
    ),  # ✅ 修复：统一使用混合认证
    tenant_service: TenantService = Depends(get_tenant_service),
) -> StandardResponse[TenantRead]:
    """
    更新租户状态

    - **tenant_id**: 租户唯一标识
    - **status_update**: 状态更新数据，包含is_active字段

    用于启用或禁用租户账户
    """
    try:
        # 租户隔离检查：用户只能更新自己的租户状态
        if current_tenant.id != tenant_id:
            logger.warning(
                "租户状态更新权限被拒绝",
                current_tenant_id=str(current_tenant.id),
                requested_tenant_id=str(tenant_id),
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有权限更新该租户状态"
            )

        # 验证状态更新数据
        if "is_active" not in status_update:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少必需的is_active字段",
            )

        # 更新租户状态
        updated_tenant = await tenant_service.update_tenant_status(
            tenant_id, is_active=status_update["is_active"]
        )

        if not updated_tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="租户不存在"
            )

        logger.info(
            "租户状态更新成功",
            tenant_id=str(tenant_id),
            new_status=status_update["is_active"],
        )

        return StandardResponse(
            success=True, message="租户状态更新成功", data=updated_tenant
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "租户状态更新异常",
            error=str(e),
            tenant_id=str(tenant_id),
            status_update=status_update,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新租户状态失败"
        )


@router.post(
    "/{tenant_id}/regenerate-api-key",
    response_model=StandardResponse[dict],
    summary="重新生成API密钥",
    description="为租户重新生成新的API密钥",
)
async def regenerate_api_key(
    tenant_id: UUID,
    current_tenant: Tenant = Depends(
        get_tenant_from_mixed_auth()
    ),  # ✅ 修复：统一使用混合认证
    tenant_service: TenantService = Depends(get_tenant_service),
) -> StandardResponse[dict]:
    """
    重新生成API密钥

    - **tenant_id**: 租户唯一标识

    为租户生成新的API密钥，旧密钥将失效
    """
    try:
        # 租户隔离检查：用户只能重新生成自己的API密钥
        if current_tenant.id != tenant_id:
            logger.warning(
                "API密钥重生成权限被拒绝",
                current_tenant_id=str(current_tenant.id),
                requested_tenant_id=str(tenant_id),
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限重新生成该租户的API密钥",
            )

        # 重新生成API密钥
        new_api_key = await tenant_service.regenerate_api_key(tenant_id)

        if not new_api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="租户不存在"
            )

        logger.info(
            "API密钥重生成成功",
            tenant_id=str(tenant_id),
            new_api_key_prefix=new_api_key[:8] + "***",
        )

        return StandardResponse(
            success=True,
            message="API密钥重新生成成功",
            data={
                "api_key": new_api_key,
                "tenant_id": str(tenant_id),
                "generated_at": "now",  # 实际应该使用真实时间戳
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("API密钥重生成异常", error=str(e), tenant_id=str(tenant_id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重新生成API密钥失败",
        )

# ================================
# 批量操作端点（性能优化）
# ================================

@router.post("/batch", response_model=list[TenantRead], tags=["批量操作"])
async def get_tenants_batch(
    tenant_ids: list[UUID],
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_admin_user),  # 仅管理员可批量操作
) -> list[TenantRead]:
    """
    批量获取租户信息（管理员功能）
    
    性能优化：单次查询获取多个租户，减少数据库往返
    """
    try:
        if len(tenant_ids) > 100:  # 限制批量大小
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="批量查询数量不能超过100个"
            )
        
        tenant_service = TenantService(db)
        
        # 使用IN查询一次性获取所有租户
        from sqlalchemy import select
        query = select(Tenant).where(Tenant.id.in_(tenant_ids))
        result = await db.execute(query)
        tenants = result.scalars().all()
        
        logger.info(
            "批量获取租户信息", 
            requested_count=len(tenant_ids),
            found_count=len(tenants),
            admin_user=str(current_user.id)
        )
        
        return [TenantRead.model_validate(tenant) for tenant in tenants]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("批量获取租户信息失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量获取租户信息失败"
        )

@router.patch("/batch/status", response_model=dict, tags=["批量操作"])
async def update_tenants_status_batch(
    update_data: dict[str, Any],  # {"tenant_ids": [uuid1, uuid2], "status": "ACTIVE"}
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_admin_user),  # 仅管理员可批量操作
) -> dict[str, Any]:
    """
    批量更新租户状态（管理员功能）
    
    性能优化：单次SQL更新多个记录
    """
    try:
        tenant_ids = update_data.get("tenant_ids", [])
        new_status = update_data.get("status")
        
        if not tenant_ids or not new_status:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须提供tenant_ids和status参数"
            )
        
        if len(tenant_ids) > 50:  # 限制批量更新大小
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="批量更新数量不能超过50个"
            )
        
        # 验证状态值
        try:
            status_enum = TenantStatus(new_status)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的状态值: {new_status}"
            )
        
        # 批量更新
        from sqlalchemy import update
        query = (
            update(Tenant)
            .where(Tenant.id.in_(tenant_ids))
            .values(status=status_enum, updated_at=func.now())
        )
        
        result = await db.execute(query)
        await db.commit()
        
        updated_count = result.rowcount
        
        logger.info(
            "批量更新租户状态",
            tenant_ids=len(tenant_ids),
            updated_count=updated_count,
            new_status=new_status,
            admin_user=str(current_user.id)
        )
        
        return {
            "message": f"成功更新 {updated_count} 个租户状态",
            "updated_count": updated_count,
            "requested_count": len(tenant_ids),
            "new_status": new_status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("批量更新租户状态失败", error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量更新租户状态失败"
        )