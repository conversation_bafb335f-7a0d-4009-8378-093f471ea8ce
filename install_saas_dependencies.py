#!/usr/bin/env python3
"""
📦 SaaS平台依赖安装工具
自动安装AstrBot SaaS平台所需的Python依赖包
"""

import subprocess
import sys
import os
from pathlib import Path


def install_dependencies():
    """安装SaaS平台依赖"""
    print("📦 安装AstrBot SaaS平台依赖...")
    
    # 切换到saas-platform目录
    saas_dir = Path(__file__).parent / "saas-platform"
    os.chdir(saas_dir)
    
    # 核心依赖列表
    core_dependencies = [
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0",
        "sqlalchemy[asyncio]>=2.0.20",
        "asyncpg>=0.29.0",
        "alembic>=1.12.0",
        "python-jose[cryptography]>=3.3.0",
        "passlib[bcrypt]>=1.7.4",
        "python-multipart>=0.0.6",
        "httpx>=0.25.0",
        "pydantic>=2.4.0",
        "pydantic-settings>=2.0.0",
        "python-dotenv>=1.0.0",
        "redis>=5.0.0",
        "psutil>=5.9.0"  # 添加psutil用于进程监控
    ]
    
    print(f"  📍 工作目录: {os.getcwd()}")
    print(f"  🐍 Python版本: {sys.version}")
    
    # 升级pip
    print("\n🔧 升级pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True, text=True)
        print("  ✅ pip升级成功")
    except subprocess.CalledProcessError as e:
        print(f"  ⚠️ pip升级失败: {e}")
    
    # 安装核心依赖
    print("\n📦 安装核心依赖...")
    failed_packages = []
    
    for package in core_dependencies:
        try:
            print(f"  📥 安装 {package}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                check=True, 
                capture_output=True, 
                text=True
            )
            print(f"    ✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"    ❌ {package} 安装失败: {e}")
            failed_packages.append(package)
    
    # 报告结果
    print("\n" + "="*50)
    if failed_packages:
        print(f"❌ {len(failed_packages)} 个包安装失败:")
        for pkg in failed_packages:
            print(f"  • {pkg}")
        print("\n💡 建议:")
        print("1. 检查网络连接")
        print("2. 尝试使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        print("3. 手动安装失败的包")
        return False
    else:
        print("✅ 所有依赖安装成功!")
        return True


def test_imports():
    """测试关键模块导入"""
    print("\n🧪 测试关键模块导入...")
    
    test_modules = [
        "fastapi",
        "uvicorn", 
        "sqlalchemy",
        "asyncpg",
        "pydantic",
        "pydantic_settings",
        "httpx",
        "psutil"
    ]
    
    failed_imports = []
    
    for module in test_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ {len(failed_imports)} 个模块导入失败")
        return False
    else:
        print("\n✅ 所有关键模块导入成功!")
        return True


def main():
    """主函数"""
    print("🚀 AstrBot SaaS平台依赖安装开始")
    print("="*50)
    
    # 安装依赖
    install_success = install_dependencies()
    
    if install_success:
        # 测试导入
        import_success = test_imports()
        
        if import_success:
            print("\n🎉 依赖安装和测试完成!")
            print("💡 现在可以尝试启动SaaS平台:")
            print("   cd saas-platform")
            print("   python simple_start.py")
            return 0
        else:
            print("\n⚠️ 依赖安装成功但导入测试失败")
            return 1
    else:
        print("\n❌ 依赖安装失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
