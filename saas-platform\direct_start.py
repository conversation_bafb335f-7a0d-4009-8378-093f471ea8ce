#!/usr/bin/env python3
"""
直接启动SaaS平台
绕过复杂的配置系统，直接设置环境变量并启动
"""

import os
import sys
from pathlib import Path

def main():
    """主函数"""
    print("🚀 直接启动 AstrBot SaaS Platform...")
    
    # 确保工作目录正确
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"📍 工作目录: {os.getcwd()}")
    
    # 设置所有必要的环境变量
    env_vars = {
        'DATABASE_URL': 'postgresql+asyncpg://astrbot:astrbot123@localhost:5432/astrbot_saas',
        'REDIS_URL': 'redis://:redis123@localhost:6379/0',
        'SECRET_KEY': '09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7',
        'FIRST_SUPERUSER': '<EMAIL>',
        'FIRST_SUPERUSER_PASSWORD': 'ChangeMeASAP!',
        'BACKEND_CORS_ORIGINS': 'http://localhost:3000,http://localhost:8080,http://localhost:6185',
        'ENVIRONMENT': 'development',
        'DEBUG': 'true',
        'LOG_LEVEL': 'INFO',
        'PROJECT_NAME': 'AstrBot SaaS Platform',
        'SERVER_HOST': '0.0.0.0',
        'SERVER_PORT': '8000',
        'POSTGRES_SERVER': 'localhost',
        'POSTGRES_USER': 'astrbot',
        'POSTGRES_PASSWORD': 'astrbot123',
        'POSTGRES_DB': 'astrbot_saas',
        'POSTGRES_PORT': '5432'
    }
    
    print("⚙️ 设置环境变量...")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  ✅ {key}={value[:20]}{'...' if len(value) > 20 else ''}")
    
    try:
        print("\n📦 启动uvicorn服务器...")
        
        # 直接使用uvicorn启动，不导入复杂的应用模块
        import uvicorn
        
        # 启动配置
        config = {
            "app": "app.main:app",
            "host": "0.0.0.0",
            "port": 8000,
            "log_level": "info",
            "access_log": True,
            "reload": False,  # 避免reload导致的问题
        }
        
        print("🌐 服务器配置:")
        for key, value in config.items():
            print(f"  • {key}: {value}")
        
        print("\n🎯 启动信息:")
        print("  🌐 服务地址: http://localhost:8000")
        print("  📚 API文档: http://localhost:8000/docs")
        print("  🔍 健康检查: http://localhost:8000/health")
        print("  ⏹️ 停止服务: Ctrl+C")
        print("\n" + "="*50)
        
        # 启动服务器
        uvicorn.run(**config)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        return 0
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
