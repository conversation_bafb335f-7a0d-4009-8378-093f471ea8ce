{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-24T10:47:27.795Z", "updatedAt": "2025-06-24T10:47:27.800Z", "resourceCount": 38}, "resources": [{"id": "devops-executor", "source": "project", "protocol": "role", "name": "Devops Executor 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/devops-executor/devops-executor.role.md", "metadata": {"createdAt": "2025-06-24T10:47:27.796Z", "updatedAt": "2025-06-24T10:47:27.796Z", "scannedAt": "2025-06-24T10:47:27.796Z"}}, {"id": "devops-executor", "source": "project", "protocol": "thought", "name": "Devops Executor 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/devops-executor/thought/devops-executor.thought.md", "metadata": {"createdAt": "2025-06-24T10:47:27.796Z", "updatedAt": "2025-06-24T10:47:27.796Z", "scannedAt": "2025-06-24T10:47:27.796Z"}}, {"id": "project-cleanup", "source": "project", "protocol": "execution", "name": "Project Cleanup 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/devops-executor/execution/project-cleanup.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.796Z", "updatedAt": "2025-06-24T10:47:27.796Z", "scannedAt": "2025-06-24T10:47:27.796Z"}}, {"id": "devops-practices", "source": "project", "protocol": "knowledge", "name": "Devops Practices 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/devops-executor/knowledge/devops-practices.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.796Z", "updatedAt": "2025-06-24T10:47:27.796Z", "scannedAt": "2025-06-24T10:47:27.796Z"}}, {"id": "project-summary-knowledge-manager", "source": "project", "protocol": "role", "name": "Project Summary Knowledge Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/project-summary-knowledge-manager/project-summary-knowledge-manager.role.md", "metadata": {"createdAt": "2025-06-24T10:47:27.796Z", "updatedAt": "2025-06-24T10:47:27.796Z", "scannedAt": "2025-06-24T10:47:27.796Z"}}, {"id": "knowledge-crystallization", "source": "project", "protocol": "thought", "name": "Knowledge Crystallization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/project-summary-knowledge-manager/thought/knowledge-crystallization.thought.md", "metadata": {"createdAt": "2025-06-24T10:47:27.796Z", "updatedAt": "2025-06-24T10:47:27.796Z", "scannedAt": "2025-06-24T10:47:27.796Z"}}, {"id": "project-synthesis", "source": "project", "protocol": "thought", "name": "Project Synthesis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/project-summary-knowledge-manager/thought/project-synthesis.thought.md", "metadata": {"createdAt": "2025-06-24T10:47:27.796Z", "updatedAt": "2025-06-24T10:47:27.796Z", "scannedAt": "2025-06-24T10:47:27.796Z"}}, {"id": "knowledge-management-standards", "source": "project", "protocol": "execution", "name": "Knowledge Management Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/project-summary-knowledge-manager/execution/knowledge-management-standards.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "project-summary-methodology", "source": "project", "protocol": "execution", "name": "Project Summary Methodology 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/project-summary-knowledge-manager/execution/project-summary-methodology.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "knowledge-engineering", "source": "project", "protocol": "knowledge", "name": "Knowledge Engineering 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/project-summary-knowledge-manager/knowledge/knowledge-engineering.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "organizational-learning", "source": "project", "protocol": "knowledge", "name": "Organizational Learning 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/project-summary-knowledge-manager/knowledge/organizational-learning.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "project-management-frameworks", "source": "project", "protocol": "knowledge", "name": "Project Management Frameworks 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/project-summary-knowledge-manager/knowledge/project-management-frameworks.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "quality-improvement-manager", "source": "project", "protocol": "role", "name": "Quality Improvement Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/quality-improvement-manager/quality-improvement-manager.role.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "quality-management", "source": "project", "protocol": "thought", "name": "Quality Management 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/quality-improvement-manager/thought/quality-management.thought.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "quality-verification", "source": "project", "protocol": "execution", "name": "Quality Verification 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/quality-improvement-manager/execution/quality-verification.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "quality-management", "source": "project", "protocol": "knowledge", "name": "Quality Management 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/quality-improvement-manager/knowledge/quality-management.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "security-architect", "source": "project", "protocol": "role", "name": "Security Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/security-architect/security-architect.role.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "security-mindset", "source": "project", "protocol": "thought", "name": "Security Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/security-architect/thought/security-mindset.thought.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "compliance-validation", "source": "project", "protocol": "execution", "name": "Compliance Validation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/security-architect/execution/compliance-validation.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "security-hardening", "source": "project", "protocol": "execution", "name": "Security Hardening 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/security-architect/execution/security-hardening.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.797Z", "updatedAt": "2025-06-24T10:47:27.797Z", "scannedAt": "2025-06-24T10:47:27.797Z"}}, {"id": "container-security", "source": "project", "protocol": "knowledge", "name": "Container Security 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/security-architect/knowledge/container-security.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.798Z", "updatedAt": "2025-06-24T10:47:27.798Z", "scannedAt": "2025-06-24T10:47:27.798Z"}}, {"id": "cybersecurity", "source": "project", "protocol": "knowledge", "name": "Cybersecurity 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/security-architect/knowledge/cybersecurity.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.798Z", "updatedAt": "2025-06-24T10:47:27.798Z", "scannedAt": "2025-06-24T10:47:27.798Z"}}, {"id": "kubernetes-security", "source": "project", "protocol": "knowledge", "name": "Kubernetes Security 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/security-architect/knowledge/kubernetes-security.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.798Z", "updatedAt": "2025-06-24T10:47:27.798Z", "scannedAt": "2025-06-24T10:47:27.798Z"}}, {"id": "technical-documentation-expert", "source": "project", "protocol": "role", "name": "Technical Documentation Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/technical-documentation-expert/technical-documentation-expert.role.md", "metadata": {"createdAt": "2025-06-24T10:47:27.798Z", "updatedAt": "2025-06-24T10:47:27.798Z", "scannedAt": "2025-06-24T10:47:27.798Z"}}, {"id": "technical-documentation-expert", "source": "project", "protocol": "thought", "name": "Technical Documentation Expert 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/technical-documentation-expert/thought/technical-documentation-expert.thought.md", "metadata": {"createdAt": "2025-06-24T10:47:27.798Z", "updatedAt": "2025-06-24T10:47:27.798Z", "scannedAt": "2025-06-24T10:47:27.798Z"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/technical-documentation-expert/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.798Z", "updatedAt": "2025-06-24T10:47:27.798Z", "scannedAt": "2025-06-24T10:47:27.798Z"}}, {"id": "git-documentation-sync", "source": "project", "protocol": "execution", "name": "Git Documentation Sync 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/technical-documentation-expert/execution/git-documentation-sync.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.798Z", "updatedAt": "2025-06-24T10:47:27.798Z", "scannedAt": "2025-06-24T10:47:27.798Z"}}, {"id": "test-architecture-expert", "source": "project", "protocol": "role", "name": "Test Architecture Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/test-architecture-expert/test-architecture-expert.role.md", "metadata": {"createdAt": "2025-06-24T10:47:27.799Z", "updatedAt": "2025-06-24T10:47:27.799Z", "scannedAt": "2025-06-24T10:47:27.799Z"}}, {"id": "test-architecture-thinking", "source": "project", "protocol": "thought", "name": "Test Architecture Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/test-architecture-expert/thought/test-architecture-thinking.thought.md", "metadata": {"createdAt": "2025-06-24T10:47:27.799Z", "updatedAt": "2025-06-24T10:47:27.799Z", "scannedAt": "2025-06-24T10:47:27.799Z"}}, {"id": "quality-assurance-standards", "source": "project", "protocol": "execution", "name": "Quality Assurance Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/test-architecture-expert/execution/quality-assurance-standards.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.799Z", "updatedAt": "2025-06-24T10:47:27.799Z", "scannedAt": "2025-06-24T10:47:27.799Z"}}, {"id": "test-system-construction", "source": "project", "protocol": "execution", "name": "Test System Construction 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/test-architecture-expert/execution/test-system-construction.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.799Z", "updatedAt": "2025-06-24T10:47:27.799Z", "scannedAt": "2025-06-24T10:47:27.799Z"}}, {"id": "quality-engineering", "source": "project", "protocol": "knowledge", "name": "Quality Engineering 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/test-architecture-expert/knowledge/quality-engineering.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.799Z", "updatedAt": "2025-06-24T10:47:27.799Z", "scannedAt": "2025-06-24T10:47:27.799Z"}}, {"id": "test-automation", "source": "project", "protocol": "knowledge", "name": "Test Automation 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/test-architecture-expert/knowledge/test-automation.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.799Z", "updatedAt": "2025-06-24T10:47:27.799Z", "scannedAt": "2025-06-24T10:47:27.799Z"}}, {"id": "testing-frameworks", "source": "project", "protocol": "knowledge", "name": "Testing Frameworks 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/test-architecture-expert/knowledge/testing-frameworks.knowledge.md", "metadata": {"createdAt": "2025-06-24T10:47:27.799Z", "updatedAt": "2025-06-24T10:47:27.799Z", "scannedAt": "2025-06-24T10:47:27.799Z"}}, {"id": "test-execution-expert", "source": "project", "protocol": "role", "name": "Test Execution Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/test-execution-expert/test-execution-expert.role.md", "metadata": {"createdAt": "2025-06-24T10:47:27.799Z", "updatedAt": "2025-06-24T10:47:27.799Z", "scannedAt": "2025-06-24T10:47:27.799Z"}}, {"id": "test-execution-thinking", "source": "project", "protocol": "thought", "name": "Test Execution Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/test-execution-expert/thought/test-execution-thinking.thought.md", "metadata": {"createdAt": "2025-06-24T10:47:27.800Z", "updatedAt": "2025-06-24T10:47:27.800Z", "scannedAt": "2025-06-24T10:47:27.800Z"}}, {"id": "quality-validation", "source": "project", "protocol": "execution", "name": "Quality Validation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/test-execution-expert/execution/quality-validation.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.800Z", "updatedAt": "2025-06-24T10:47:27.800Z", "scannedAt": "2025-06-24T10:47:27.800Z"}}, {"id": "test-implementation", "source": "project", "protocol": "execution", "name": "Test Implementation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/test-execution-expert/execution/test-implementation.execution.md", "metadata": {"createdAt": "2025-06-24T10:47:27.800Z", "updatedAt": "2025-06-24T10:47:27.800Z", "scannedAt": "2025-06-24T10:47:27.800Z"}}], "stats": {"totalResources": 38, "byProtocol": {"role": 7, "thought": 8, "execution": 12, "knowledge": 11}, "bySource": {"project": 38}}}